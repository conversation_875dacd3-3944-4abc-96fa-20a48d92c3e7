#!/bin/bash
# Thunderbird Log Deduplication Script
# Removes duplicate entries from Thunderbird.log based on various strategies

set -euo pipefail

# Default values
INPUT_FILE=""
OUTPUT_FILE=""
STRATEGY="exact"
WINDOW_SECONDS=60
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print usage
usage() {
    echo "Usage: $0 [OPTIONS] INPUT_FILE"
    echo ""
    echo "Deduplicate Thunderbird log files using various strategies"
    echo ""
    echo "OPTIONS:"
    echo "  -o, --output FILE     Output file (default: INPUT_FILE.deduplicated)"
    echo "  -s, --strategy TYPE   Deduplication strategy:"
    echo "                        exact    - Remove exact duplicate lines (default)"
    echo "                        message  - Remove duplicates based on message only"
    echo "                        semantic - Remove duplicates based on message patterns"
    echo "                        hash     - Remove duplicates based on content hash"
    echo "                        node     - Remove duplicates based on node+message"
    echo "                        time     - Remove duplicates within time window"
    echo "  -w, --window SECONDS  Time window for time-based dedup (default: 60)"
    echo "  -v, --verbose         Verbose output"
    echo "  -h, --help           Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 dataset/Thunderbird/Thunderbird.log"
    echo "  $0 -s semantic -o clean.log dataset/Thunderbird/Thunderbird.log"
    echo "  $0 -s node dataset/Thunderbird/Thunderbird.log"
}

# Function to log messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to parse Thunderbird log line and extract message
parse_thunderbird_message() {
    local line="$1"
    # Thunderbird format: alert_category timestamp date node message
    echo "$line" | cut -d' ' -f5-
}

# Function to parse Thunderbird log line and extract node
parse_thunderbird_node() {
    local line="$1"
    echo "$line" | cut -d' ' -f4
}

# Function to parse Thunderbird log line and extract alert_category + node + message
parse_thunderbird_content() {
    local line="$1"
    local alert_category=$(echo "$line" | cut -d' ' -f1)
    local node=$(echo "$line" | cut -d' ' -f4)
    local message=$(echo "$line" | cut -d' ' -f5-)
    echo "${alert_category}|${node}|${message}"
}

# Function to create semantic pattern from message (Thunderbird-specific)
create_thunderbird_pattern() {
    local message="$1"
    
    # Replace process IDs in square brackets
    message=$(echo "$message" | sed 's/\[[0-9]\+\]/[PID]/g')
    
    # Replace numbers with <NUM>
    message=$(echo "$message" | sed 's/\b[0-9]\+\b/<NUM>/g')
    
    # Replace hex addresses
    message=$(echo "$message" | sed 's/\b0x[0-9a-fA-F]\+\b/<HEX>/g')
    
    # Replace IP addresses
    message=$(echo "$message" | sed 's/\b[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\b/<IP>/g')
    
    # Replace file paths
    message=$(echo "$message" | sed 's|/[^[:space:]]\+|<PATH>|g')
    
    # Replace dates and times
    message=$(echo "$message" | sed 's/\b[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}\b/<DATE>/g')
    message=$(echo "$message" | sed 's/\b[0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}\b/<TIME>/g')
    
    # Replace Thunderbird-specific node patterns
    message=$(echo "$message" | sed 's/\ben[0-9]\+\b/<EN_NODE>/g')
    message=$(echo "$message" | sed 's/\bcn[0-9]\+\b/<CN_NODE>/g')
    message=$(echo "$message" | sed 's/\bbn[0-9]\+\b/<BN_NODE>/g')
    
    # Replace session and user identifiers
    message=$(echo "$message" | sed 's/session [^[:space:]]\+/session <ID>/g')
    message=$(echo "$message" | sed 's/user [^[:space:]]\+/user <USER>/g')
    
    echo "$message"
}

# Function to deduplicate using exact match
deduplicate_exact() {
    local input="$1"
    local output="$2"
    
    log_info "Deduplicating using exact match strategy..."
    
    # Use sort and uniq to remove exact duplicates
    sort "$input" | uniq > "$output"
    
    local original_lines=$(wc -l < "$input")
    local unique_lines=$(wc -l < "$output")
    local duplicates=$((original_lines - unique_lines))
    
    log_success "Removed $duplicates duplicate lines ($original_lines -> $unique_lines)"
}

# Function to deduplicate based on message only
deduplicate_message() {
    local input="$1"
    local output="$2"
    
    log_info "Deduplicating using message-only strategy..."
    
    local temp_file=$(mktemp)
    local seen_messages=$(mktemp)
    
    local line_count=0
    local duplicates=0
    
    while IFS= read -r line; do
        ((line_count++))
        
        if [[ $((line_count % 100000)) -eq 0 ]]; then
            log_info "Processed $line_count lines..."
        fi
        
        # Extract message (field 5 onwards)
        local message=$(parse_thunderbird_message "$line")
        
        # Check if we've seen this message before
        if ! grep -Fxq "$message" "$seen_messages" 2>/dev/null; then
            echo "$message" >> "$seen_messages"
            echo "$line" >> "$temp_file"
        else
            ((duplicates++))
        fi
    done < "$input"
    
    mv "$temp_file" "$output"
    rm -f "$seen_messages"
    
    local unique_lines=$(wc -l < "$output")
    log_success "Removed $duplicates duplicate lines ($line_count -> $unique_lines)"
}

# Function to deduplicate based on semantic patterns
deduplicate_semantic() {
    local input="$1"
    local output="$2"
    
    log_info "Deduplicating using semantic pattern strategy..."
    
    local temp_file=$(mktemp)
    local seen_patterns=$(mktemp)
    
    local line_count=0
    local duplicates=0
    
    while IFS= read -r line; do
        ((line_count++))
        
        if [[ $((line_count % 100000)) -eq 0 ]]; then
            log_info "Processed $line_count lines..."
        fi
        
        # Extract message and create pattern
        local message=$(parse_thunderbird_message "$line")
        local pattern=$(create_thunderbird_pattern "$message")
        
        # Check if we've seen this pattern before
        if ! grep -Fxq "$pattern" "$seen_patterns" 2>/dev/null; then
            echo "$pattern" >> "$seen_patterns"
            echo "$line" >> "$temp_file"
        else
            ((duplicates++))
        fi
    done < "$input"
    
    mv "$temp_file" "$output"
    rm -f "$seen_patterns"
    
    local unique_lines=$(wc -l < "$output")
    log_success "Removed $duplicates duplicate lines ($line_count -> $unique_lines)"
}

# Function to deduplicate based on content hash
deduplicate_hash() {
    local input="$1"
    local output="$2"
    
    log_info "Deduplicating using content hash strategy..."
    
    local temp_file=$(mktemp)
    local seen_hashes=$(mktemp)
    
    local line_count=0
    local duplicates=0
    
    while IFS= read -r line; do
        ((line_count++))
        
        if [[ $((line_count % 100000)) -eq 0 ]]; then
            log_info "Processed $line_count lines..."
        fi
        
        # Extract content (alert_category + node + message)
        local content=$(parse_thunderbird_content "$line")
        local hash=$(echo "$content" | md5sum | cut -d' ' -f1)
        
        # Check if we've seen this hash before
        if ! grep -Fxq "$hash" "$seen_hashes" 2>/dev/null; then
            echo "$hash" >> "$seen_hashes"
            echo "$line" >> "$temp_file"
        else
            ((duplicates++))
        fi
    done < "$input"
    
    mv "$temp_file" "$output"
    rm -f "$seen_hashes"
    
    local unique_lines=$(wc -l < "$output")
    log_success "Removed $duplicates duplicate lines ($line_count -> $unique_lines)"
}

# Function to deduplicate based on node + message (Thunderbird-specific)
deduplicate_node() {
    local input="$1"
    local output="$2"
    
    log_info "Deduplicating using node+message strategy..."
    
    local temp_file=$(mktemp)
    local seen_combinations=$(mktemp)
    
    local line_count=0
    local duplicates=0
    
    while IFS= read -r line; do
        ((line_count++))
        
        if [[ $((line_count % 100000)) -eq 0 ]]; then
            log_info "Processed $line_count lines..."
        fi
        
        # Extract node and message
        local node=$(parse_thunderbird_node "$line")
        local message=$(parse_thunderbird_message "$line")
        local combination="${node}|${message}"
        
        # Check if we've seen this node+message combination before
        if ! grep -Fxq "$combination" "$seen_combinations" 2>/dev/null; then
            echo "$combination" >> "$seen_combinations"
            echo "$line" >> "$temp_file"
        else
            ((duplicates++))
        fi
    done < "$input"
    
    mv "$temp_file" "$output"
    rm -f "$seen_combinations"
    
    local unique_lines=$(wc -l < "$output")
    log_success "Removed $duplicates duplicate lines ($line_count -> $unique_lines)"
}

# Function to deduplicate based on time window (simplified)
deduplicate_time() {
    local input="$1"
    local output="$2"
    
    log_info "Deduplicating using time window strategy (${WINDOW_SECONDS}s window)..."
    log_warning "Time-based deduplication in bash is simplified - using pattern frequency instead"
    
    local temp_file=$(mktemp)
    local pattern_counts=$(mktemp)
    
    local line_count=0
    local duplicates=0
    
    while IFS= read -r line; do
        ((line_count++))
        
        if [[ $((line_count % 100000)) -eq 0 ]]; then
            log_info "Processed $line_count lines..."
        fi
        
        # Extract node and create message pattern
        local node=$(parse_thunderbird_node "$line")
        local message=$(parse_thunderbird_message "$line")
        local pattern=$(create_thunderbird_pattern "$message")
        local key="${node}|${pattern}"
        
        # Count occurrences of this pattern from this node
        local count=$(grep -c "^$key$" "$pattern_counts" 2>/dev/null || echo "0")
        
        # Allow up to 3 similar messages from same node
        if [[ $count -lt 3 ]]; then
            echo "$key" >> "$pattern_counts"
            echo "$line" >> "$temp_file"
        else
            ((duplicates++))
        fi
    done < "$input"
    
    mv "$temp_file" "$output"
    rm -f "$pattern_counts"
    
    local unique_lines=$(wc -l < "$output")
    log_success "Removed $duplicates duplicate lines ($line_count -> $unique_lines)"
}

# Function to print statistics
print_stats() {
    local input="$1"
    local output="$2"
    
    if [[ -f "$input" && -f "$output" ]]; then
        local original_lines=$(wc -l < "$input")
        local unique_lines=$(wc -l < "$output")
        local duplicates=$((original_lines - unique_lines))
        local duplicate_percentage=$(( (duplicates * 100) / original_lines ))
        
        local original_size=$(stat -f%z "$input" 2>/dev/null || stat -c%s "$input")
        local output_size=$(stat -f%z "$output" 2>/dev/null || stat -c%s "$output")
        local size_reduction=$(( ((original_size - output_size) * 100) / original_size ))
        
        echo ""
        echo "=== Thunderbird Deduplication Statistics ==="
        echo "Total lines processed: $(printf "%'d" $original_lines)"
        echo "Duplicates removed: $(printf "%'d" $duplicates)"
        echo "Unique lines remaining: $(printf "%'d" $unique_lines)"
        echo "Duplicate percentage: ${duplicate_percentage}%"
        echo "File size reduction: ${size_reduction}%"
        echo "Original size: $(printf "%'d" $original_size) bytes"
        echo "Deduplicated size: $(printf "%'d" $output_size) bytes"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -s|--strategy)
            STRATEGY="$2"
            shift 2
            ;;
        -w|--window)
            WINDOW_SECONDS="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
        *)
            INPUT_FILE="$1"
            shift
            ;;
    esac
done

# Validate input
if [[ -z "$INPUT_FILE" ]]; then
    log_error "Input file is required"
    usage
    exit 1
fi

if [[ ! -f "$INPUT_FILE" ]]; then
    log_error "Input file '$INPUT_FILE' not found"
    exit 1
fi

# Set default output file
if [[ -z "$OUTPUT_FILE" ]]; then
    OUTPUT_FILE="${INPUT_FILE}.deduplicated"
fi

# Validate strategy
case "$STRATEGY" in
    exact|message|semantic|hash|node|time)
        ;;
    *)
        log_error "Invalid strategy: $STRATEGY"
        usage
        exit 1
        ;;
esac

# Main execution
log_info "Starting Thunderbird log deduplication"
log_info "Input file: $INPUT_FILE"
log_info "Output file: $OUTPUT_FILE"
log_info "Strategy: $STRATEGY"

case "$STRATEGY" in
    exact)
        deduplicate_exact "$INPUT_FILE" "$OUTPUT_FILE"
        ;;
    message)
        deduplicate_message "$INPUT_FILE" "$OUTPUT_FILE"
        ;;
    semantic)
        deduplicate_semantic "$INPUT_FILE" "$OUTPUT_FILE"
        ;;
    hash)
        deduplicate_hash "$INPUT_FILE" "$OUTPUT_FILE"
        ;;
    node)
        deduplicate_node "$INPUT_FILE" "$OUTPUT_FILE"
        ;;
    time)
        deduplicate_time "$INPUT_FILE" "$OUTPUT_FILE"
        ;;
esac

print_stats "$INPUT_FILE" "$OUTPUT_FILE"
log_success "Deduplication complete! Output saved to: $OUTPUT_FILE"
